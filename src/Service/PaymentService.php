<?php

namespace App\Service;

use DateTimeZone;
use Exception;
use Psr\Log\LoggerInterface;

class PaymentService
{
  public function __construct(
    private DeviceService $deviceService,
    private TransactionService $transactionService,
    private LoyaltyService $loyaltyService,
    private FiscalService $fiscalService,
    private LoyaltyNoFundsEmailSender $loyaltyNoFundsEmailSender,
    private LocationService $locationService,
    private LoggerInterface $logger,
  ) {}

  public function processGVendPayToken($record, $device, $deviceConfiguration, $company, $deviceTags, $location) {
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);
    $messageSaldo = $record->getAmount();

    if (!$device['service_mode'] or !$record->getIsTest()) {
      // set daily saldo pay card
      $deviceDailySaldo["token"] = number_format(($deviceDailySaldo['token'] ?? 0) + $messageSaldo, 2, '.', '');
      $this->deviceService->updateToken($tokens, $device["id"]);
      $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

      // set transaction
      $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'pay_token',
        $device["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );
    }

  }


  public function processPayCoin($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    // check if message is pay_coin & parallel type
    if (array_key_exists('parallel', $config)) {
      // calculate saldo
      if ($device["is_saldo_reducer"]) {
        foreach ($data as $impuls) {
          if (array_key_exists($impuls['line'], $config['parallel']['input_values'])) {
            $messageSaldo -= (float)number_format(
              $config['parallel']['input_values'][$impuls['line']],
              2
            );
            $newSaldo -= (float)number_format(
              $config['parallel']['input_values'][$impuls['line']],
              2
            );
          }
        }

        $newSaldo = str_replace(',', '.', (string)$newSaldo);
        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $device["fiscal"] ? $device["bill_number"] + 1 : $device["bill_number"], $device["id"]);

        $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_coin',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );
      } else {
        foreach ($data as $impuls) {
          if (array_key_exists($impuls['line'], $config['parallel']['input_values'])) {
            $messageSaldo += (float)number_format(
              $config['parallel']['input_values'][$impuls['line']],
              2
            );
            $newSaldo += (float)number_format(
              $config['parallel']['input_values'][$impuls['line']],
              2
            );
          }
        }

        $newSaldo = str_replace(',', '.', (string)$newSaldo);

        // fiscal
        if (!$device["service_mode"] and !$data[0]['service_mode'] and (float)number_format(
          $messageSaldo,
          2
        ) != 0.00) {

          $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);

          // update device saldo
          $billNumber = $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"];
          $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $billNumber, $device["id"]);

          $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
          $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

          $transactionId = null;
          // set transaction
          try {
            $transactionId = $this->transactionService->setTransaction(
              number_format(
                $messageSaldo,
                2,
                '.',
                ''
              ),
              'pay_coin',
              $deviceConfiguration["imei"],
              $company['oib'],
              $device,
              $location,
              $deviceTags,
              'null'
            );
          } catch (Exception $e) {
            $this->logger->error("transaction not set" . " " . $e->getMessage());
          }

          if ($isDeviceFiscal) {
            $this->fiscalService->fiscal(
              $company,
              $deviceConfiguration["imei"],
              $device["bill_number"],
              $device["business_space_label"],
              $device["isu_number"],
              number_format(
                $messageSaldo,
                2,
                '.',
                ''
              ),
              'coin',
              $transactionId
            );
          }
        }
      }
    }
    if (array_key_exists('pulse', $config)) {
      if ($device["is_saldo_reducer"]) {
        foreach ($data as $key => $impuls) {
          if (array_key_exists($impuls['line'], $config['pulse'])) {
            $messageSaldo -= (float)number_format(
              $config['pulse'][$impuls['line']]['value'],
              2
            );
            $newSaldo -= (float)number_format(
              $config['pulse'][$impuls['line']]['value'],
              2
            );
          }
        }

        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $device["fiscal"] ? $device["bill_number"] + 1 : $device["bill_number"], $device["id"]);

        $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_coin',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );
      } else {
        foreach ($data as $impuls) {
          if (array_key_exists($impuls['line'], $config['pulse'])) {
            $messageSaldo += (float)number_format(
              $config['pulse'][$impuls['line']]['value'],
              2
            );
            $newSaldo += (float)number_format(
              $config['pulse'][$impuls['line']]['value'],
              2
            );
          }
        }

        // fiscalization
        if (!$device["service_mode"] and !$data[0]['service_mode'] and (float)number_format(
          $messageSaldo,
          2
        ) != 0.00) {

          $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);
          // update device saldo
          $billNumber = $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"];
          $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $billNumber, $device["id"]);

          // set daily saldo pay coin
          $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
          $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

          // set transaction
          $transactionId = $this->transactionService->setTransaction(
            number_format(
              $messageSaldo,
              2,
              '.',
              ''
            ),
            'pay_coin',
            $deviceConfiguration["imei"],
            $company['oib'] ?? "",
            $device,
            $location,
            $deviceTags,
            'null'
          );

          if ($isDeviceFiscal) {
            $this->fiscalService->fiscal(
              $company,
              $deviceConfiguration["imei"],
              $device["bill_number"],
              $device["business_space_label"],
              $device["isu_number"],
              number_format(
                $messageSaldo,
                2,
                '.',
                ''
              ),
              'coin',
              $transactionId
            );
          }
        }
      }
    }
  }

  public function processPayCCTalk($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {

    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    if (array_key_exists('cctalk', $config)) {
      if ($device["is_saldo_reducer"]) {
        foreach ($data as $impuls) {
          if (array_key_exists(
            $impuls['payload'],
            $config['cctalk']['output_values']
          )) {
            $messageSaldo -= (float)number_format(
              $config['cctalk']['output_values'][$impuls['payload']],
              2
            );
            $newSaldo -= (float)number_format(
              $config['cctalk']['output_values'][$impuls['payload']],
              2
            );
          }
        }

        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $device["fiscal"] ? $device["bill_number"] + 1 : $device["bill_number"], $device["id"]);

        $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_cctalk',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );
      } else {
        foreach ($data as $impuls) {
          if (array_key_exists(
            $impuls['payload'],
            $config['cctalk']['output_values']
          )) {
            $messageSaldo += (float)number_format(
              $config['cctalk']['output_values'][$impuls['payload']],
              2
            );
            $newSaldo += (float)number_format(
              $config['cctalk']['output_values'][$impuls['payload']],
              2
            );
          }
        }

        if (!$device["service_mode"] and !$data[0]['service_mode'] and (float)number_format(
          $messageSaldo,
          2
        ) > 0.00) {

          // set daily saldo pay coin
          $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
          $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

          // set transaction
          $transactionId = $this->transactionService->setTransaction(
            number_format(
              $messageSaldo,
              2,
              '.',
              ''
            ),
            'pay_cctalk',
            $deviceConfiguration["imei"],
            $company['oib'],
            $device,
            $location,
            $deviceTags,
            'null'
          );

          $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);
          $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"], $device["id"]);

          if ($isDeviceFiscal) {
            $this->fiscalService->fiscal(
              $company,
              $deviceConfiguration["imei"],
              $device["bill_number"],
              $device["business_space_label"],
              $device["isu_number"],
              number_format(
                $messageSaldo,
                2,
                '.',
                ''
              ),
              'coin',
              $transactionId
            );
          }
        }
      }
    }
  }

  public function processPaySMS($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    $messageSaldo += (float)number_format($config['start-game']['value'], 2);
    $newSaldo += (float)number_format($config['start-game']['value'], 2);

    if (!$device['service_mode']) {
      // if(number_format($messageSaldo, 2) > 0.00){
      //   $this->deviceService->updateBillNumber($device["fiscal"] ? $device["bill_number"] + 1 : $device["bill_number"], $device["id"]);
      // }

      // set daily saldo pay sms
      $deviceDailySaldo["sms"] = number_format(($deviceDailySaldo['sms'] ?? 0) + $messageSaldo, 2, '.', '');
      $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

      // set transaction
      $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'pay_sms',
        $deviceConfiguration["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );

      // 08.01.2025 -> SMS payment is discontinued. We still save the transaction to retain historical data for free rides, but we skip the fiscal part.
      // $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_sms',  $company['is_top_gun']);

      // if ($isDeviceFiscal) {
      //   $this->fiscalService->fiscal(
      //     $company,
      //     $deviceConfiguration["imei"],
      //     $device["bill_number"],
      //     $device["business_space_label"],
      //     $device["isu_number"],
      //     number_format(
      //       $messageSaldo,
      //       2,
      //       '.',
      //       ''
      //     ),
      //     'sms',
      //     $transactionId
      //   );
      // }
    }
  }

  public function processPayCard($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    if (array_key_exists('pulse', $config)) {

      foreach ($data as $impuls) {
        if (array_key_exists($impuls['line'], $config['pulse'])) {
          $messageSaldo += (float)number_format(
            $config['pulse'][$impuls['line']]['value'],
            2
          );
        }
      }
    } else {
      return;
    }


    if (!$device['service_mode']) {
      $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_card',  $company['is_top_gun']);
      if ($isDeviceFiscal) {
        $this->deviceService->updateBillNumber($device["bill_number"] + 1, $device["id"]);
      }

      // set daily saldo pay card
      $deviceDailySaldo["card"] = number_format(($deviceDailySaldo['card'] ?? 0) + $messageSaldo, 2, '.', '');
      $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

      // set transaction
      $transactionId = $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'pay_card',
        $deviceConfiguration["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );

      if ($isDeviceFiscal) {
        $this->fiscalService->fiscal(
          $company,
          $deviceConfiguration["imei"],
          $device["bill_number"],
          $device["business_space_label"],
          $device["isu_number"],
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'card',
          $transactionId
        );
      }
    }
  }

  public function processPayToken($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    $tokens = $device["token"] ?? 0;

    $config = json_decode($deviceConfiguration["configuration"], true);

    if (array_key_exists('pulse', $config)) {
      foreach ($data as $impuls) {
        if (array_key_exists($impuls['line'], $config['pulse'])) {
          $messageSaldo += (float)number_format(
            $config['pulse'][$impuls['line']]['value'],
            2
          );
          $tokens += $config['pulse'][$impuls['line']]['value'];
        }
      }
    } else {
      return;
    }


    if (!$device['service_mode']) {
      // set daily saldo pay card
      $deviceDailySaldo["token"] = number_format(($deviceDailySaldo['token'] ?? 0) + $messageSaldo, 2, '.', '');
      $this->deviceService->updateToken($tokens, $device["id"]);
      $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

      // set transaction
      $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'pay_token',
        $deviceConfiguration["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );
    }
  }

  public function processPayRfCard($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    // check if device is in service mode
    if (!$device['service_mode']) {
      // get card details
      $card = $this->loyaltyService->getCard($data[0]['card_id']);

      // check if card is active
      if ($card['isActive']) {
        if (10 == $card['saldo']) {
          if ($card['isVerified']) {
            $this->loyaltyNoFundsEmailSender->sendTopUp($card['user']['email'], $card);
          }
        }

        // // send notification email to user if card saldo is 10
        if (5 == $card['saldo']) {
          if ($card['isVerified']) {
            $this->loyaltyNoFundsEmailSender->sendTopUp($card['user']['email'], $card);
          }
        }

        if (1 == $card['saldo']) {
          if ($card['isVerified']) {
            // send notification email to user
            $this->loyaltyNoFundsEmailSender->send($card['user']['email'], $card);
          }
        }

        // check if card have min saldo for one transaction
        if ($card['saldo'] >= (float)number_format($config['loyalty_input_value'], 2)) {

          // update card saldo
          $this->loyaltyService->updateSaldo(
            $card['id'],
            $card['saldo'],
            $config['loyalty_input_value'],
            $card['totalSpent'],
            $card['saldoBonus'],
            $card['saldoReal']
          );
        }

        $deviceDailySaldo["rfCard"] = number_format($config['loyalty_input_value'] + $messageSaldo, 2, '.', '');
        $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

        $this->transactionService->setTransaction(
          number_format($config['loyalty_input_value'], 2),
          'pay_rfcard',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          $card['serialNumber']
        );
      }
    }
  }

  public function processPayMdb($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);


    if (array_key_exists('mdb', $config)) {
      foreach ($data as $impuls) {
        if (array_key_exists($impuls['payload'], $config['mdb']['input_values'])) {
          $messageSaldo += (float)number_format(
            $config['mdb']['input_values'][$impuls['payload']],
            2
          );
          $newSaldo += (float)number_format(
            $config['mdb']['input_values'][$impuls['payload']],
            2
          );
        }
      }

      if (!$device['service_mode'] and !$data[0]['service_mode'] and (float)number_format(
        $messageSaldo,
        2
      ) > 0.00) {
        $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);
        // update device saldo
        $billNumber = $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"];
        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $billNumber, $device["id"]);

        $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
        $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

        // set transaction
        $transactionId = $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_mdb',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );

        if ($isDeviceFiscal) {
          $this->fiscalService->fiscal(
            $company,
            $deviceConfiguration["imei"],
            $device["bill_number"],
            $device["business_space_label"],
            $device["isu_number"],
            number_format(
              $messageSaldo,
              2,
              '.',
              ''
            ),
            'coin',
            $transactionId
          );
        }
      }
    }
  }

  public function processPayNayaxCard($payedValue, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    $messageSaldo += (float)number_format($payedValue, 2);
    $newSaldo += (float)number_format($payedValue, 2);


    if (!$device['service_mode']) {
      $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_card',  $company['is_top_gun']);
      if ($isDeviceFiscal) {
        $this->deviceService->updateBillNumber($device["bill_number"] + 1, $device["id"]);
      }

      // set daily saldo pay card
      $deviceDailySaldo["card"] = number_format(($deviceDailySaldo['card'] ?? 0) + $messageSaldo, 2, '.', '');
      $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

      // set transaction
      $transactionId = $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'pay_card',
        $deviceConfiguration["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );

      $this->logger->warning(json_encode(["info" => "Nayax transaction has been created for imei", "imei" => $deviceConfiguration["imei"] ?? null]));

      if ($isDeviceFiscal) {
        $this->fiscalService->fiscal(
          $company,
          $deviceConfiguration["imei"],
          $device["bill_number"],
          $device["business_space_label"],
          $device["isu_number"],
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'card',
          $transactionId
        );
      }
    }
  }

  public function processPaySci($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);

    if (array_key_exists('sci', $config)) {
      foreach ($data as $impuls) {
        if ('C4D1E0F08?' == $impuls['payload']) {
          $messageSaldo += 0.05;
          $newSaldo += 0.05;
        }

        if ('C3D1E0F08?' == $impuls['payload']) {
          $messageSaldo += 0.10;
          $newSaldo += 0.10;
        }

        if ('C2D1E0F08?' == $impuls['payload']) {
          $messageSaldo += 0.20;
          $newSaldo += 0.20;
        }

        if ('C1D1E0F08?' == $impuls['payload']) {
          $messageSaldo += 0.50;
          $newSaldo += 0.50;
        }

        if ('C0D1E0F08?' == $impuls['payload']) {
          $messageSaldo += 1.00;
          $newSaldo += 1.00;
        }

        // if ($impuls['payload'] == 'C9D1E0F08?') {
        if ('C9D0E0F08?' == $impuls['payload']) {
          $messageSaldo += 2.00;
          $newSaldo += 2.00;
        }
      }

      if (!$device['service_mode'] and !$data[0]['service_mode'] and (float)number_format(
        $messageSaldo,
        2
      ) > 0.00) {
        $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);
        $deviceBillNumber = $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"];

        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $deviceBillNumber, $device["id"]);

        $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
        $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

        // set transaction
        $transactionId = $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_coin',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );

        if ($isDeviceFiscal) {
          $this->fiscalService->fiscal(
            $company,
            $deviceConfiguration["imei"],
            $device["bill_number"],
            $device["business_space_label"],
            $device["isu_number"],
            number_format(
              $messageSaldo,
              2,
              '.',
              ''
            ),
            'coin',
            $transactionId
          );
        }
      }
    }
  }

  public function processPayExec($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $newSaldo = $device["saldo"];

    $config = json_decode($deviceConfiguration["configuration"], true);
    $deviceDailySaldo = json_decode($device["daily_saldo"], true);


    if (array_key_exists('exec', $config)) {
      foreach ($data as $impuls) {
        if (array_key_exists($impuls['payload'], $config['exec']['output_values'])) {
          $messageSaldo += (float)number_format(
            $config['exec']['output_values'][$impuls['payload']],
            2
          );
          $newSaldo += (float)number_format(
            $config['exec']['output_values'][$impuls['payload']],
            2
          );
        }
      }

      // fiscalization
      if (!$device["service_mode"] and !$data[0]['service_mode'] and (float)number_format(
        $messageSaldo,
        2
      ) > 0.00) {
        $isDeviceFiscal = $this->fiscalCheck($device, $location, 'pay_coin',  $company['is_top_gun']);
        $billNumber = $isDeviceFiscal ? $device["bill_number"] + 1 : $device["bill_number"];

        $this->deviceService->updateSaldoAndBillNumber(number_format($newSaldo, 2, '.', ''), $billNumber, $device["id"]);

        $deviceDailySaldo["coin"] = number_format(($deviceDailySaldo['coin'] ?? 0) + $messageSaldo, 2, '.', '');
        $this->deviceService->updateDailySaldo(json_encode($deviceDailySaldo), $device["id"]);

        // set transaction
        $transactionId = $this->transactionService->setTransaction(
          number_format(
            $messageSaldo,
            2,
            '.',
            ''
          ),
          'pay_coin',
          $deviceConfiguration["imei"],
          $company['oib'],
          $device,
          $location,
          $deviceTags,
          'null'
        );

        if ($isDeviceFiscal) {
          $this->fiscalService->fiscal(
            $company,
            $deviceConfiguration["imei"],
            $device["bill_number"],
            $device["business_space_label"],
            $device["isu_number"],
            number_format(
              $messageSaldo,
              2,
              '.',
              ''
            ),
            'coin',
            $transactionId
          );
        }
      }
    }
  }

  public function processTokenDrop($data, $device, $deviceConfiguration, $company, $deviceTags, $location)
  {
    $messageSaldo = 0;
    $tokens = $device["token"] ?? 0;

    $config = json_decode($deviceConfiguration["configuration"], true);

    if (!isset($data[0]['payload'])) {
      foreach ($data as $drop) {
        if (isset($config["pulse"]) && array_key_exists($drop['line'], $config['pulse'])) {
          $messageSaldo += (float)number_format($config['pulse'][$drop['line']]['value'], 2);

          if ($tokens > 0) {
            $tokens -= $config['pulse'][$drop['line']]['value'];
          }
        } elseif (isset($config["parallel"]) && array_key_exists($drop['line'], $config['parallel']['token_values'])) {
          $messageSaldo += (float)number_format($config['parallel']['token_values'][$drop['line']], 2);

          if ($tokens > 0) {
            $tokens -= $config['parallel']['token_values'][$drop['line']];
          }
        }
      }
    } else {
      if (array_key_exists('cctalk', $config)) {
        foreach ($data as $impuls) {
          if (array_key_exists($impuls['payload'], $config['cctalk']['token_values'])) {
            $messageSaldo += (float)number_format(
              $config['cctalk']['token_values'][$impuls['payload']],
              2
            );

            if ($tokens > 0) {
              $tokens -= $config['cctalk']['token_values'][$impuls['payload']];
            }
          }
        }
      }
    }

    if (!$device["service_mode"] and !$data[0]['service_mode']) {
      $this->deviceService->updateToken($tokens < 0 ? 0 : $tokens, $device["id"]);
      $this->transactionService->setTransaction(
        number_format(
          $messageSaldo,
          2,
          '.',
          ''
        ),
        'token_drop',
        $deviceConfiguration["imei"],
        $company['oib'],
        $device,
        $location,
        $deviceTags,
        'null'
      );
    }
  }

  public function countTransactions($location, $paymentType)
  {
    $paymentProperty = null;
    switch ($paymentType) {
      case 'pay_sms':
        $paymentProperty = 'smsCount';
        break;
      case 'pay_coin':
        $paymentProperty = 'coinCount';
        break;
      case 'pay_card':
        $paymentProperty = 'cardCount';
        break;
      default:
        break;
    }

    try {
      if ($paymentProperty) {
        $maintenanceMetaToUpdate = null;
        if (isset($location['maintenance_meta'])) {
          $maintenanceMetaDecoded = json_decode($location['maintenance_meta'], true);
          if (isset($maintenanceMetaDecoded[$paymentProperty])) {
            $maintenanceMetaToUpdate = array_merge($maintenanceMetaDecoded, [$paymentProperty => $maintenanceMetaDecoded[$paymentProperty] + 1]);
          } else {
            $maintenanceMetaToUpdate = array_merge($maintenanceMetaDecoded,  [$paymentProperty => 1]);
          }
        } else {
          $maintenanceMetaToUpdate = [$paymentProperty => 1];
        }
        if ($maintenanceMetaToUpdate) {
          $this->locationService->updateMaintenanceMeta($location['id'], $maintenanceMetaToUpdate);
        }
      }
      $this->logger->warning(json_encode(["info" => "Counting transactions succeded for trasnaction ", "locationId" => $location['id'] ?? null]));
    } catch (Exception $e) {
      $this->logger->warning(json_encode(["error" => "Counting transactions error: " . $e->getMessage(), "locationId" => $location['id'] ?? null]));
    }
  }

  //The old fiscal check
  // public function fiscalCheck($device, $location, $paymentType, $isTopGun)
  // {

  //   if (!$isTopGun) {
  //     return true;
  //   }

  //   $paymentNthTransaction = null;

  //   switch ($paymentType) {
  //     case 'pay_sms':
  //       $paymentNthTransaction = 'smsNthTransaction';
  //       break;
  //     case 'pay_coin':
  //       $paymentNthTransaction = 'coinNthTransaction';
  //       break;
  //     case 'pay_card':
  //       $paymentNthTransaction = 'cardNthTransaction';
  //       break;
  //     default:
  //       break;
  //   }

  //   if (!$device['fiscal']) {
  //     return false;
  //   } else if (isset($location['maintenance_meta'])) {
  //     $maintenanceMetaDecoded = json_decode($location['maintenance_meta'], true);
  //     if (isset($maintenanceMetaDecoded[$paymentNthTransaction]) && is_numeric($maintenanceMetaDecoded[$paymentNthTransaction]) && $maintenanceMetaDecoded[$paymentNthTransaction] - 1 > 0) {
  //       $locationNthTransactionBills =  $this->transactionService->getTransactionsByLocationAndPaymentType($location['id'], $paymentType, $maintenanceMetaDecoded[$paymentNthTransaction] - 1);
  //       if (in_array(null, array_column($locationNthTransactionBills, "bill"), true)) {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     }
  //   }

  //   return true;
  // }

  public function fiscalCheck($device, $location, $paymentType, $isTopGun)
  {
    if (!$isTopGun) {
      return true;
    }

    $paymentNthTransaction = null;

    switch ($paymentType) {
      case 'pay_sms':
        $paymentNthTransaction = 'smsNthTransaction';
        break;
      case 'pay_coin':
        $paymentNthTransaction = 'coinNthTransaction';
        break;
      case 'pay_card':
        $paymentNthTransaction = 'cardNthTransaction';
        break;
      default:
        break;
    }

    if (!$device['fiscal']) {
      return false;
    } else {
      $currentDateTime = new \DateTime('now');
      $currentDateTime->setTimezone(new DateTimeZone(('Europe/Zagreb')));
      $currentDay = strtolower($currentDateTime->format('l'));

      $startTimeMethodName = 'tg_' . $currentDay . '_start_time';
      $endTimeMethodName = 'tg_' . $currentDay . '_end_time';

      $currentTime = $currentDateTime->format('H:i');

      $this->logger->warning(json_encode(["info" => "Fiscal check", "currentTime" => $currentTime ?? null, "topGunStartTime" => $device[$startTimeMethodName] ?? null,  "topGunEndTime" => $device[$endTimeMethodName] ?? null]));

      if (($device[$startTimeMethodName] && !$device[$endTimeMethodName]) || (!$device[$startTimeMethodName] && $device[$endTimeMethodName])) {
        return true;
      }

      if ($device[$startTimeMethodName] && $currentTime >= $device[$startTimeMethodName] && $device[$endTimeMethodName] && $currentTime <= $device[$endTimeMethodName]) {
        return true;
      } else if (isset($location['maintenance_meta'])) {
        $maintenanceMetaDecoded = json_decode($location['maintenance_meta'], true);
        if (isset($maintenanceMetaDecoded[$paymentNthTransaction]) && is_numeric($maintenanceMetaDecoded[$paymentNthTransaction]) && $maintenanceMetaDecoded[$paymentNthTransaction] - 1 > 0) {
          $locationNthTransactionBills =  $this->transactionService->getTransactionsByLocationAndPaymentType($location['id'], $paymentType, $maintenanceMetaDecoded[$paymentNthTransaction] - 1);
          if (in_array(null, array_column($locationNthTransactionBills, "bill"), true)) {
            return true;
          } else {
            return false;
          }
        }
      }
    }

    return true;
  }
}
