<?php

namespace App\Command;

use App\Repository\GVendRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\DeviceService;
use App\Service\DepositService;
use App\Service\CompanyService;

#[AsCommand(name: 'app:gvend-monitor')]
class GVendMonitorCommand extends Command
{
    protected static $defaultName = 'app:gvend-monitor';

    public function __construct(
        private GVendRepository $gVendRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger,
        private ParameterBagInterface $params,
        private DeviceService $deviceService,
        private DepositService $depositService,
        private CompanyService $companyService,
        private PaymentService $paymentService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Monitor GVend table for new records and process them')
            ->addOption(
                'interval',
                'i',
                InputOption::VALUE_OPTIONAL,
                'Monitoring interval in seconds',
                null
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_OPTIONAL,
                'Number of records to process in each batch',
                null
            )
            ->addOption(
                'single-run',
                's',
                InputOption::VALUE_NONE,
                'Run once and exit (for testing)'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Get configuration parameters
        $interval = $input->getOption('interval') ?? $this->params->get('gvend.monitor.default_interval');
        $batchSize = $input->getOption('batch-size') ?? $this->params->get('gvend.monitor.default_batch_size');
        $singleRun = $input->getOption('single-run');

        $io->title('GVend Monitor Started');
        $io->info(sprintf('Monitoring interval: %d seconds', $interval));
        $io->info(sprintf('Batch size: %d records', $batchSize));

        if ($singleRun) {
            $io->info('Running in single-run mode');
        }

        $this->logger->info('GVend Monitor started', [
            'interval' => $interval,
            'batch_size' => $batchSize,
            'single_run' => $singleRun
        ]);

        $iteration = 0;

        do {
            $iteration++;
            $startTime = microtime(true);

            try {
                // Check database connection
                $this->checkDatabaseConnection();

                // Process unprocessed records
                $processedCount = $this->processUnprocessedRecords($batchSize, $io);

                $endTime = microtime(true);
                $processingTime = round(($endTime - $startTime) * 1000, 2);

                if ($processedCount > 0) {
                    $io->success(sprintf(
                        'Iteration %d: Processed %d records in %s ms',
                        $iteration,
                        $processedCount,
                        $processingTime
                    ));

                    $this->logger->info('GVend records processed', [
                        'iteration' => $iteration,
                        'processed_count' => $processedCount,
                        'processing_time_ms' => $processingTime
                    ]);
                } else {
                    $io->text(sprintf(
                        'Iteration %d: No new records found (checked in %s ms)',
                        $iteration,
                        $processingTime
                    ));
                }

                if (!$singleRun) {
                    sleep($interval);
                }

            } catch (\Exception $e) {
                $io->error(sprintf('Error in iteration %d: %s', $iteration, $e->getMessage()));
                $this->logger->error('GVend Monitor error', [
                    'iteration' => $iteration,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                if (!$singleRun) {
                    sleep($interval);
                }
            }

        } while (!$singleRun);

        $io->success('GVend Monitor completed');
        $this->logger->info('GVend Monitor stopped');

        return Command::SUCCESS;
    }

    private function processUnprocessedRecords(int $batchSize, SymfonyStyle $io): int
    {
        $unprocessedRecords = $this->gVendRepository->findUnprocessed($batchSize);

        if (empty($unprocessedRecords)) {
            return 0;
        }

        $io->text(sprintf('Found %d unprocessed records', count($unprocessedRecords)));

        // For now, just log the records - processing logic will be added later
        foreach ($unprocessedRecords as $record) {
            $this->logger->info('Found unprocessed GVend record', [
                'id' => $record->getId(),
                'imei' => $record->getImei(),
                'amount' => $record->getAmount(),
                'payment_type' => $record->getPaymentType(),
                'type' => $record->getType(),
                'created' => $record->getCreated()->format('Y-m-d H:i:s'),
                'proc' => $record->getProc()
            ]);

            $device = $this->deviceService->getByLoggerImei($record->getImei());
             if (!$device) {
                $this->logger->warning("Device not found for IMEI: " . $record->getImei(), ["context" => __CLASS__]);
                continue;
            }

            $company = $this->companyService->getById($device["company_id"]);

            if (!$company) {
                $this->logger->warning("Company not found for company_id: " . $device["company_id"], ["context" => __CLASS__]);
                continue;
            }

            $deviceEventMeta = json_decode($device["event_meta"], true);
            $deviceTags = $this->deviceTagService->getTagsByDeviceId($device["id"]);
            $location = $this->locationService->getById($device["location_id"]);

            $valuesToBeUpdated = [];
            if ($device["is_problematic"]) {
                $valuesToBeUpdated["is_problematic"] = false;
            }

            switch($record->getPaymentType()) {
                case 'ping':
                    $deviceEventMeta['lastPing'] = $record->getCreated()->format('Y-m-d H:i:s');
                    $valuesToBeUpdated["is_critical"] = false;
                    break;
                case 'empty':
                    $deviceEventMeta['lastEmpty'] = $record->getCreated()->format('Y-m-d H:i:s');
                    if ($device["saldo"] != 0 or ($device["token"] != 0 and !$device["is_monster_device"])) {
                        $this->depositService->create($device["saldo"], $device["id"], $device["is_monster_device"] ? null : $device["token"]);
                    }
                    if (!$device["is_monster_device"]) {
                        $valuesToBeUpdated["token"] = 0;
                    }
                    $valuesToBeUpdated["saldo"] = 0;
                    $this->deviceService->processEmpty($record->getImei(), $company);
                    break;
                case 'pay_coin':
                    $deviceEventMeta['lastPayCoin'] = $record->getCreated()->format('Y-m-d H:i:s');
                    $deviceEventMeta['lastPay'] = $record->getCreated()->format('Y-m-d H:i:s');
                    $deviceEventMeta['lastPayType'] = 'pay_coin';
                    $this->paymentService->processGVendPayCoin($record, $device, $deviceConfiguration, $company, $deviceTags, $location);
                    $valuesToBeUpdated["is_critical"] = false;
                    break;
            }

        }



        // TODO: Add actual processing logic here
        // For now, we're just fetching and logging the records

        return count($unprocessedRecords);
    }

    private function checkDatabaseConnection(): void
    {
        try {
            // Test database connection
            $this->entityManager->getConnection()->executeQuery('SELECT 1');
        } catch (\Exception $e) {
            $this->logger->warning('Database connection lost, attempting to reconnect');
            $this->entityManager->getConnection()->close();
            $this->entityManager->getConnection()->connect();
        }
    }
}
